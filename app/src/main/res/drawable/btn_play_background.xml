<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 聚焦状态 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FFFFFFFF"
                android:endColor="#F5F5F5"
                android:angle="270" />
            <corners android:radius="16dp" />
            <stroke android:width="2dp" android:color="@color/accent_color" />
        </shape>
    </item>
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#E0E0E0" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FFFFFFFF"
                android:endColor="#F8F8F8"
                android:angle="270" />
            <corners android:radius="16dp" />
        </shape>
    </item>
</selector>
